import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { useAppStorage } from "../../../hooks/useAppStorage";
import appLogo from "../../../assets/app-logo.svg";
import SignatureMethod from "./SignApplication/SignatureMethod";
import {
  formatCurrency,
  formatPhoneNumber,
  parsePhoneNumber,
  formatEIN,
  parseEIN,
  maskEIN,
  formatSSN,
  parseSSN,
  maskSSN,
} from "../../../utils/formatters";
import { entityTypeOptions, industryOptions, stateOptions, ficoOptions } from "../../../utils/consts";
import { validationSchema } from "../../../utils/validationSchema";

/**
 * Sign Application step component
 * This component displays a custom HTML document template for signing
 *
 * @param {Object} props
 * @param {string} props.appId - Application ID
 * @returns {JSX.Element}
 */
export const SignApplication = ({ appId }) => {
  const [editingField, setEditingField] = useState(null); // Track which field is being edited
  const [signature, setSignature] = useState("");
  const [signatureMethod, setSignatureMethod] = useState("type"); // Track signature method

  const { watch, setValue, trigger } = useFormContext();
  const formData = watch();
  const { updateApplicationForm } = useAppStorage();

  // Get current date for signature
  const currentDate = new Date().toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  // Handle field editing
  const handleFieldEdit = async (fieldPath, newValue) => {
    setValue(fieldPath, newValue);
    updateApplicationForm({ [fieldPath]: newValue });
    setEditingField(null);

    // Trigger validation for the field
    await trigger(fieldPath);
  };

  // Get field configuration for formatting and validation
  const getFieldConfig = (fieldPath) => {
    const configs = {
      businessPhone: {
        type: "phone",
        formatter: formatPhoneNumber,
        parser: parsePhoneNumber,
        validation: validationSchema.businessPhone,
      },
      businessEmail: {
        type: "email",
        validation: validationSchema.businessEmail,
      },
      ein: {
        type: "ein",
        formatter: formatEIN,
        parser: parseEIN,
        validation: validationSchema.ein,
      },
      entityType: {
        type: "select",
        options: entityTypeOptions,
        validation: validationSchema.entityType,
      },
      industry: {
        type: "select",
        options: industryOptions,
        validation: validationSchema.industry,
      },
      businessStartDate: {
        type: "date",
        validation: validationSchema.businessStartDate,
      },
      "owners.0.phone": {
        type: "phone",
        formatter: formatPhoneNumber,
        parser: parsePhoneNumber,
        validation: validationSchema.phone,
      },
      "owners.1.phone": {
        type: "phone",
        formatter: formatPhoneNumber,
        parser: parsePhoneNumber,
        validation: validationSchema.phone,
      },
      "owners.0.email": {
        type: "email",
        validation: validationSchema.email,
      },
      "owners.1.email": {
        type: "email",
        validation: validationSchema.email,
      },
      "owners.0.dateOfBirth": {
        type: "date",
        validation: validationSchema["owners[0].dateOfBirth"],
      },
      "owners.1.dateOfBirth": {
        type: "date",
        validation: validationSchema["owners[1].dateOfBirth"],
      },
      "owners.0.ownershipPercentage": {
        type: "percentage",
        validation: validationSchema["owners[0].ownershipPercentage"],
      },
      "owners.1.ownershipPercentage": {
        type: "percentage",
        validation: validationSchema["owners[1].ownershipPercentage"],
      },
      "owners.0.ssn": {
        type: "ssn",
        formatter: formatSSN,
        parser: parseSSN,
        validation: validationSchema.ssn,
      },
      "owners.0.creditScore": {
        type: "select",
        options: ficoOptions,
        validation: validationSchema["owners[0].creditScore"],
      },
      "owners.1.ssn": {
        type: "ssn",
        formatter: formatSSN,
        parser: parseSSN,
        validation: validationSchema.ssn,
      },
      businessDBA: {
        type: "text",
        validation: validationSchema.dbaName,
      },
      "businessAddress.line1": {
        type: "text",
        validation: validationSchema["address.line1"],
      },
      "businessAddress.city": {
        type: "text",
        validation: validationSchema["address.city"],
      },
      "businessAddress.state": {
        type: "select",
        options: stateOptions,
        validation: validationSchema["address.state"],
      },
      "businessAddress.zip": {
        type: "text",
        validation: validationSchema["address.zip"],
      },
      "owners.0.address.line1": {
        type: "text",
        validation: validationSchema["address.line1"],
      },
      "owners.0.address.city": {
        type: "text",
        validation: validationSchema["address.city"],
      },
      "owners.0.address.state": {
        type: "select",
        options: stateOptions,
        validation: validationSchema["address.state"],
      },
      "owners.0.address.zip": {
        type: "text",
        validation: validationSchema["address.zip"],
      },
      "owners.1.address.line1": {
        type: "text",
        validation: validationSchema["address.line1"],
      },
      "owners.1.address.city": {
        type: "text",
        validation: validationSchema["address.city"],
      },
      "owners.1.address.state": {
        type: "select",
        options: stateOptions,
        validation: validationSchema["address.state"],
      },
      "owners.1.address.zip": {
        type: "text",
        validation: validationSchema["address.zip"],
      },
    };
    return configs[fieldPath] || { type: "text" };
  };

  const FormattedInput = ({ config, value, hasError, onSave, onCancel }) => {
    const [inputValue, setInputValue] = useState(() => {
      // Initialize with formatted value for display
      if (config.type === "currency" || config.type === "phone" || config.type === "ein" || config.type === "ssn") {
        return config.formatter ? config.formatter(value) : value || "";
      }
      return value || "";
    });

    const handleChange = (e) => {
      const newValue = e.target.value;

      switch (config.type) {
        case "currency":
          setInputValue(formatCurrency(newValue));
          break;
        case "phone":
          setInputValue(formatPhoneNumber(newValue));
          break;
        case "ein":
          setInputValue(formatEIN(newValue));
          break;
        case "ssn":
          setInputValue(formatSSN(newValue));
          break;
        case "percentage": {
          const percentValue = newValue.replace(/[^\d%]/g, "");
          setInputValue(percentValue);
          break;
        }
        default:
          setInputValue(newValue);
      }
    };

    const handleKeyDown = (e) => {
      if (e.key === "Enter") {
        onSave(inputValue);
      } else if (e.key === "Escape") {
        onCancel();
      }
    };

    const getInputProps = () => {
      const baseProps = {
        value: inputValue,
        onChange: handleChange,
        onBlur: () => onSave(inputValue),
        onKeyDown: handleKeyDown,
        className: `inline-block min-w-0 bg-white border rounded px-1 py-0.5 text-sm focus:outline-none focus:ring-1 ${
          hasError
            ? "border-red-500 focus:ring-red-500 focus:border-red-500"
            : "border-blue-500 focus:ring-blue-500 focus:border-blue-500"
        }`,
        autoFocus: true,
      };

      // Add specific props for different input types
      switch (config.type) {
        case "currency":
          return {
            ...baseProps,
            type: "text",
            inputMode: "numeric",
            placeholder: "$50,000",
          };
        case "phone":
          return {
            ...baseProps,
            type: "tel",
            inputMode: "tel",
            placeholder: "(*************",
            maxLength: "14", // (XXX) XXX-XXXX
          };
        case "ein":
          return {
            ...baseProps,
            type: "text",
            inputMode: "numeric",
            placeholder: "XX-XXXXXXX",
            maxLength: "10", // XX-XXXXXXX
          };
        case "ssn":
          return {
            ...baseProps,
            type: "text",
            inputMode: "numeric",
            placeholder: "XXX-XX-XXXX",
            maxLength: "11", // XXX-XX-XXXX
          };
        case "percentage":
          return {
            ...baseProps,
            type: "text",
            inputMode: "numeric",
            placeholder: "50%",
            maxLength: "4", // 100%
          };
        case "email":
          return {
            ...baseProps,
            type: "email",
            inputMode: "email",
            placeholder: "<EMAIL>",
          };
        case "date":
          return {
            ...baseProps,
            type: "date",
          };
        default:
          return {
            ...baseProps,
            type: "text",
          };
      }
    };

    return <input {...getInputProps()} />;
  };

  // Enhanced EditableValue component with formatting and validation
  const EditableValue = ({ fieldPath, value, placeholder = "N/A", className = "font-semibold text-gray-900" }) => {
    const isEditing = editingField === fieldPath;
    const config = getFieldConfig(fieldPath);

    // Format value for display
    const formatDisplayValue = (val) => {
      if (!val) return val;

      switch (config.type) {
        case "currency":
          return config.formatter ? config.formatter(val) : val;
        case "phone":
          return config.formatter ? config.formatter(val) : val;
        case "ein":
          return config.formatter ? maskEIN(val) : val;
        case "ssn":
          return config.formatter ? maskSSN(val) : val;
        case "percentage":
          return `${val}%`;
        default:
          return val;
      }
    };

    // Parse value for form submission
    const parseInputValue = (inputVal) => {
      switch (config.type) {
        case "currency":
          return config.parser ? config.parser(inputVal) : inputVal;
        case "phone":
          return config.parser ? config.parser(inputVal) : inputVal;
        case "ein":
          return config.parser ? config.parser(inputVal) : inputVal;
        case "ssn":
          return config.parser ? config.parser(inputVal) : inputVal;
        case "percentage":
          return String(inputVal).replace("%", "");
        default:
          return inputVal;
      }
    };

    // Validate input value
    const validateValue = (inputVal) => {
      if (!config.validation) return true;

      const parsedVal = parseInputValue(inputVal);

      // Check required
      if (config.validation.required && !parsedVal) {
        return config.validation.required;
      }

      // Check pattern (for email)
      if (config.validation.pattern && !config.validation.pattern.value.test(parsedVal)) {
        return config.validation.pattern.message;
      }

      // Check custom validation
      if (config.validation.validate) {
        for (const validator of Object.values(config.validation.validate)) {
          const result = validator(parsedVal);
          if (result !== true) {
            return result;
          }
        }
      }

      return true;
    };

    if (isEditing) {
      const hasError = false; // TODO

      if (config.type === "select") {
        return (
          <select
            defaultValue={value}
            className={`inline-block min-w-0 bg-white border rounded px-1 py-0.5 text-sm focus:outline-none focus:ring-1 max-w-[240px] ${
              hasError
                ? "border-red-500 focus:ring-red-500 focus:border-red-500"
                : "border-blue-500 focus:ring-blue-500 focus:border-blue-500"
            }`}
            autoFocus
            onBlur={(e) => {
              const validation = validateValue(e.target.value);
              if (validation === true) {
                handleFieldEdit(fieldPath, e.target.value);
              } else {
                setEditingField(null);
              }
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                const validation = validateValue(e.target.value);
                if (validation === true) {
                  handleFieldEdit(fieldPath, e.target.value);
                } else {
                  setEditingField(null);
                }
              } else if (e.key === "Escape") {
                setEditingField(null);
              }
            }}
          >
            {config.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      }

      return (
        <FormattedInput
          config={config}
          value={value}
          hasError={hasError}
          onSave={(newValue) => {
            const validation = validateValue(newValue);
            if (validation === true) {
              const parsedValue = parseInputValue(newValue);
              handleFieldEdit(fieldPath, parsedValue);
            } else {
              setEditingField(null);
            }
          }}
          onCancel={() => setEditingField(null)}
        />
      );
    }

    const hasError = false; // TODO

    return (
      <div className="inline-block">
        <span
          className={`${className} cursor-pointer hover:bg-blue-50 hover:text-blue-700 rounded px-1 py-0.5 transition-colors print:cursor-default print:hover:bg-transparent print:hover:text-black print:text-black print:px-0 print:py-0 print:rounded-none ${
            hasError ? "text-red-600 bg-red-50 print:text-black print:bg-transparent" : ""
          }`}
          onClick={() => setEditingField(fieldPath)}
          title="Click to edit"
        >
          {formatDisplayValue(value) || placeholder}
        </span>
      </div>
    );
  };

  // Get business information
  const businessInfo = {
    name: formData?.businessName || "",
    dbaName: formData?.dbaName || "",
    website: formData?.website || "",
    entityType: formData?.entityType || "",
    ein: formData?.ein || "",
    industry: formData?.industry || "",
    startDate: formData?.businessStartDate || "",
    phone: formData?.businessPhone || "",
    email: formData?.businessEmail || "",
    address: formData.address,
  };

  // Get owner information
  const owner1 = formData?.owners?.[0] || {};
  const owner2 = formData?.owners?.[1] || null;

  return (
    <div className="space-y-6 print:space-y-4">
      <h3 className="text-xl font-semibold my-8 text-center print:hidden">Sign Your Application</h3>

      {/* Custom Document Template */}
      <div className="bg-white border border-gray-300 rounded-sm shadow-lg print:shadow-none print:border-0 print:rounded-none">
        {/* Header */}
        <div className="border-b border-gray-200 p-6 print:p-4 print:border-b-2 print:border-gray-800">
          <div className="flex flex-col lg:flex-row justify-between gap-4 pt-8 print:pt-0 print:gap-2">
            {/* Logo */}
            <div className="flex-shrink-0">
              <img src={appLogo} alt="Pinnacle Funding" className="p-1 h-12 w-auto print:h-10 print:p-0" />
            </div>

            {/* Contact Information */}
            <div className="text-left print:text-right">
              <div className="grid grid-cols-[auto_1fr] gap-x-2 gap-y-1 text-sm max-w-md print:text-xs print:gap-y-0.5">
                <span className="font-medium text-gray-600 print:text-black print:font-semibold">Phone:</span>
                <span className="font-semibold print:text-black">(*************</span>

                <span className="font-medium text-gray-600 print:text-black print:font-semibold">Email:</span>
                <span className="font-semibold print:text-black"><EMAIL></span>

                <span className="font-medium text-gray-600 print:text-black print:font-semibold">Website:</span>
                <a
                  href="https://pinnaclefundingco.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-semibold hover:underline print:text-black print:no-underline"
                >
                  PinnacleFundingCo.com
                </a>
              </div>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-4 text-xs text-gray-600 print:text-black print:mt-3 print:text-[10px] print:leading-tight">
            There are no fees, charges, or obligations associated with obtaining a pre-approval. Pre-approval does not
            constitute a funding commitment.
          </div>
        </div>

        {/* Body */}
        <div className="p-6 print:p-4">
          <div className="space-y-6 print:space-y-4">
            {/* Business Information - Full Width */}
            <div className="bg-gray-50 p-4 rounded-sm print:bg-white print:border print:border-gray-300 print:rounded-none print:p-3">
              <h4 className="text-lg font-semibold mb-3 text-gray-800 print:text-base print:mb-2 print:text-black print:border-b print:border-gray-400 print:pb-1">
                Business Information
              </h4>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-6 gap-y-2 text-sm print:text-xs print:gap-y-1 print:gap-x-4">
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                    Business Legal Name:
                  </span>{" "}
                  <EditableValue fieldPath="businessName" value={businessInfo.name} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                    Business DBA Name:
                  </span>{" "}
                  <EditableValue fieldPath="dbaName" value={businessInfo.dbaName} placeholder="N/A" />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">Address:</span>{" "}
                  <EditableValue fieldPath="businessAddress.line1" value={businessInfo.address?.line1} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">City:</span>{" "}
                  <EditableValue fieldPath="businessAddress.city" value={businessInfo.address?.city} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">State:</span>{" "}
                  <EditableValue fieldPath="businessAddress.state" value={businessInfo.address?.state} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">Zip:</span>{" "}
                  <EditableValue fieldPath="businessAddress.zip" value={businessInfo.address?.zip} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">Email:</span>{" "}
                  <EditableValue fieldPath="businessEmail" value={businessInfo.email} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">Phone:</span>{" "}
                  <EditableValue fieldPath="businessPhone" value={businessInfo.phone} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">Legal Entity:</span>{" "}
                  <EditableValue fieldPath="entityType" value={businessInfo.entityType} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">Industry:</span>{" "}
                  <EditableValue fieldPath="industry" value={businessInfo.industry} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                    Business Start Date:
                  </span>{" "}
                  <EditableValue fieldPath="businessStartDate" value={businessInfo.startDate} />
                </div>
                <div className="print:break-inside-avoid">
                  <span className="font-medium text-gray-600 print:text-black print:font-semibold">EIN:</span>{" "}
                  <EditableValue fieldPath="ein" value={businessInfo.ein} />
                </div>
              </div>
            </div>

            {/* Owner Sections */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 print:gap-4 print:break-inside-avoid">
              {/* Owner 1 Information */}
              <div className="bg-gray-50 p-4 rounded-sm print:bg-white print:border print:border-gray-300 print:rounded-none print:p-3 print:break-inside-avoid">
                <h4 className="text-lg font-semibold mb-3 text-gray-800 print:text-base print:mb-2 print:text-black print:border-b print:border-gray-400 print:pb-1">
                  Owner 1 Information
                </h4>
                <div className="space-y-2 text-sm print:text-xs print:space-y-1">
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">First Name:</span>{" "}
                    <EditableValue fieldPath="owners.0.firstName" value={owner1.firstName} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">Last Name:</span>{" "}
                    <EditableValue fieldPath="owners.0.lastName" value={owner1.lastName} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">Address:</span>{" "}
                    <EditableValue fieldPath="owners.0.address.line1" value={owner1.address?.line1} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">City:</span>{" "}
                    <EditableValue fieldPath="owners.0.address.city" value={owner1.address?.city} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">State:</span>{" "}
                    <EditableValue fieldPath="owners.0.address.state" value={owner1.address?.state} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">Zip:</span>{" "}
                    <EditableValue fieldPath="owners.0.address.zip" value={owner1.address?.zip} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                      Ownership Percent:
                    </span>{" "}
                    <EditableValue fieldPath="owners.0.ownershipPercentage" value={owner1.ownershipPercentage} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                      Date of Birth:
                    </span>{" "}
                    <EditableValue fieldPath="owners.0.dateOfBirth" value={owner1.dateOfBirth} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">SSN:</span>{" "}
                    <EditableValue fieldPath="owners.0.ssn" value={owner1.ssn} />
                  </div>
                  <div className="print:break-inside-avoid">
                    <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                      Credit Score:
                    </span>{" "}
                    <EditableValue fieldPath="owners.0.creditScore" value={owner1?.creditScore} />
                  </div>
                </div>
              </div>

              {/* Owner 2 Information (if exists) */}
              {owner2 && (
                <div className="bg-gray-50 p-4 rounded-sm print:bg-white print:border print:border-gray-300 print:rounded-none print:p-3 print:break-inside-avoid">
                  <h4 className="text-lg font-semibold mb-3 text-gray-800 print:text-base print:mb-2 print:text-black print:border-b print:border-gray-400 print:pb-1">
                    Owner 2 Information
                  </h4>
                  <div className="space-y-2 text-sm print:text-xs print:space-y-1">
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                        First Name:
                      </span>{" "}
                      <EditableValue fieldPath="owners.1.firstName" value={owner2.firstName} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">Last Name:</span>{" "}
                      <EditableValue fieldPath="owners.1.lastName" value={owner2.lastName} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">Address:</span>{" "}
                      <EditableValue fieldPath="owners.1.address.line1" value={owner2.address?.line1} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">City:</span>{" "}
                      <EditableValue fieldPath="owners.1.address.city" value={owner2.address?.city} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">State:</span>{" "}
                      <EditableValue fieldPath="owners.1.address.state" value={owner2.address?.state} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">Zip:</span>{" "}
                      <EditableValue fieldPath="owners.1.address.zip" value={owner2.address?.zip} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                        Ownership Percent:
                      </span>{" "}
                      <EditableValue fieldPath="owners.1.ownershipPercentage" value={owner2.ownershipPercentage} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">
                        Date of Birth:
                      </span>{" "}
                      <EditableValue fieldPath="owners.1.dateOfBirth" value={owner2.dateOfBirth} />
                    </div>
                    <div className="print:break-inside-avoid">
                      <span className="font-medium text-gray-600 print:text-black print:font-semibold">SSN:</span>{" "}
                      <EditableValue fieldPath="owners.1.ssn" value={owner2.ssn} />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6 bg-gray-50 print:bg-white print:border-t-2 print:border-gray-800 print:p-4">
          <div className="space-y-4 print:space-y-3">
            {/* Disclaimer */}
            <div className="text-sm text-gray-700 print:text-black print:text-xs print:leading-tight">
              <p className="mb-4 print:mb-3">
                By signing below, I acknowledge that I have read and understand the terms and conditions of this funding
                application. I certify that all information provided is true and accurate to the best of my knowledge. I
                understand that this application does not guarantee funding approval and that additional documentation
                may be required.
              </p>
            </div>

            {/* Signature Method Selection */}
            <div className="mb-6 print:hidden">
              <label className="block text-sm font-medium text-gray-700 mb-2">Signature Method</label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="type"
                    checked={signatureMethod === "type"}
                    onChange={(e) => setSignatureMethod(e.target.value)}
                    className="mr-2"
                  />
                  Type Signature
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    value="sign"
                    checked={signatureMethod === "sign"}
                    onChange={(e) => setSignatureMethod(e.target.value)}
                    className="mr-2"
                  />
                  Draw Signature
                </label>
              </div>
            </div>

            {/* Signature Section */}
            <div className="space-y-4 print:space-y-3 print:flex print:justify-between print:items-end">
              <div className="w-full print:w-auto print:flex-1 print:mr-8">
                <div className="print:hidden">
                  <SignatureMethod
                    signatureMethod={signatureMethod}
                    signature={signature}
                    setSignature={setSignature}
                  />
                </div>
                <div className="hidden print:block">
                  <label className="block text-sm font-semibold text-black mb-2 print:text-xs">Signature</label>
                  <div className="w-full h-16 border-b-2 border-black flex items-end pb-2 print:h-12 print:min-w-[300px]">
                    {signature && (
                      <span className="text-lg font-signature text-black print:text-base">{signature}</span>
                    )}
                  </div>
                </div>
              </div>
              <div className="w-full max-w-xs print:w-auto print:max-w-none print:flex-shrink-0">
                <label className="block text-sm font-medium text-gray-700 mb-1 print:text-black print:font-semibold print:text-xs">
                  Date of Signing
                </label>
                <div className="w-full p-3 border border-gray-300 rounded text-sm bg-gray-50 font-medium print:border-b-2 print:border-black print:border-t-0 print:border-l-0 print:border-r-0 print:bg-transparent print:rounded-none print:p-2 print:text-xs print:text-black print:min-w-[150px]">
                  {currentDate}
                </div>
              </div>
            </div>

            {/* Application UUID */}
            <div className="text-xs text-gray-500 mt-4 print:text-black print:text-[10px] print:mt-3">
              Application UUID: {appId.toUpperCase()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
