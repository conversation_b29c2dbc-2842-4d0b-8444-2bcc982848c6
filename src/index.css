@import "tailwindcss";

@theme {
  --font-lexend: "Lexend Variable", sans-serif;
  --font-caveat: "Caveat", cursive;
}

button {
  cursor: pointer;
}

/* Print-specific styles for SignApplication */
@media print {
  @page {
    margin: 0.75in;
    size: letter;
  }

  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Ensure proper page breaks */
  .print\:break-inside-avoid {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .print\:break-before-page {
    break-before: page;
    page-break-before: always;
  }

  /* Font signature styling for print */
  .font-signature {
    font-family: "Caveat", cursive;
    font-weight: 600;
  }

  /* Ensure black text in print */
  .print\:text-black {
    color: #000 !important;
  }

  /* Remove shadows and rounded corners for print */
  .print\:shadow-none {
    box-shadow: none !important;
  }

  .print\:rounded-none {
    border-radius: 0 !important;
  }

  /* Ensure borders are visible in print */
  .print\:border-gray-800 {
    border-color: #1f2937 !important;
  }

  .print\:border-gray-400 {
    border-color: #9ca3af !important;
  }

  .print\:border-gray-300 {
    border-color: #d1d5db !important;
  }

  .print\:border-black {
    border-color: #000 !important;
  }
}
